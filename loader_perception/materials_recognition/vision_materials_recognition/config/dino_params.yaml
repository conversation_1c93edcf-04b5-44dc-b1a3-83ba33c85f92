# DINO Feature Extractor Node Configuration
# This file contains all configurable parameters for the DINO feature extractor node

dino_feature_extractor_node:
  ros__parameters:
    # Camera topics configuration
    image_topic: "/camera_sensor/image_raw"              # RGB camera image topic
    camera_info_topic: "/camera_sensor/depth/camera_info"     # Camera info topic
    features_topic: "/dino_feature_extractor_node/image_features"            # Output features topic
    
    # DINO model configuration
    device: "cuda"                               # Device: "cuda" or "cpu"
    backbone: "dinov2"                          # Backbone type: "dino" or "dinov2"
    backbone_type: "vit_base_reg"              # ViT type: "vit_base_reg", "vit_large_reg", etc.
    input_size: 448                            # Input image size (square)
    patch_size: 14                             # ViT patch size
    projection_type: ""                        # Projection type: "" (None) or "nonlinear"
    dropout_p: 0.0                            # Dropout probability
    
# Alternative configurations for different scenarios:

# High-performance configuration (larger model, higher accuracy)
# dino_feature_extractor_node:
#   ros__parameters:
#     device: "cuda"
#     backbone: "dinov2"
#     backbone_type: "vit_large_reg"
#     input_size: 518
#     patch_size: 14
#     projection_type: "nonlinear"
#     dropout_p: 0.1

# CPU-friendly configuration (smaller model, faster inference)
# dino_feature_extractor_node:
#   ros__parameters:
#     device: "cpu"
#     backbone: "dinov2"
#     backbone_type: "vit_small_reg"
#     input_size: 224
#     patch_size: 14
#     projection_type: ""
#     dropout_p: 0.0

# Original DINO configuration
# dino_feature_extractor_node:
#   ros__parameters:
#     device: "cuda"
#     backbone: "dino"
#     backbone_type: "vit_base"
#     input_size: 224
#     patch_size: 8
#     projection_type: ""
#     dropout_p: 0.0 