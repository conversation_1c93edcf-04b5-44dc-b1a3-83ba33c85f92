import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2
import requests
from PIL import Image
from sklearn.decomposition import PCA
from pathlib import Path
import warnings

from dino_feature_extractor import DinoFeatureExtractor

warnings.filterwarnings('ignore')

def download_image(url, save_path):
    """Download image from URL and save locally."""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"Downloaded: {save_path}")
        return True
    except Exception as e:
        print(f"Failed to download {url}: {e}")
        return False

def preprocess_image_for_dino(img_pil, input_size=448):
    """Convert PIL image to tensor format expected by DINO."""
    from torchvision import transforms as T
    
    transform = T.Compose([
        T.Resize((input_size, input_size)),
        T.ToTensor(),
        # Note: DinoFeatureExtractor will apply its own normalization
    ])
    
    return transform(img_pil)

def visualize_pca_features(original_imgs, features, patch_size=14, save_dir="~/loader_ws/src/debug/pca_analysis/", backbone_type="neco_on_dinov2r_vit14_model"):
    """Visualize PCA analysis of DINO features."""
    
    # Create save directory
    save_path = Path(save_dir).expanduser()
    save_path.mkdir(parents=True, exist_ok=True)
    
    # Convert features to numpy
    if isinstance(features, torch.Tensor):
        features = features.cpu().numpy()
    
    N = len(original_imgs)
    
    # Calculate feature map dimensions
    # Assuming square input and features are [B, num_patches, C]
    num_patches = features.shape[1]
    featmap_size = int(np.sqrt(num_patches))  # e.g., 32x32 for 448x448 input with patch_size=14
    
    print(f"Feature map size: {featmap_size}x{featmap_size}")
    print(f"Feature dimension: {features.shape[2]}")
    
    plt.figure(figsize=(20, 4 * N))
    
    for idx, img in enumerate(original_imgs):
        feats = features[idx]  # [num_patches, feature_dim]
        
        # Apply PCA
        pca = PCA(n_components=3)
        pca_feats = pca.fit_transform(feats)  # [num_patches, 3]
        
        # Reshape to feature map
        pca_feats = pca_feats.reshape(featmap_size, featmap_size, 3)
        
        # Normalize each component to [0,1]
        pca_norm = np.zeros_like(pca_feats, dtype=np.float32)
        for c in range(3):
            arr = pca_feats[:, :, c]
            pca_norm[:, :, c] = (arr - arr.min()) / (arr.max() - arr.min())
        
        # Resize to original image dimensions for overlay
        pca_resized = cv2.resize(pca_norm, (img.width, img.height), interpolation=cv2.INTER_LINEAR)
        
        # Plot 5 subplots for each image
        # 1. Original image
        plt.subplot(N, 5, idx * 5 + 1)
        plt.imshow(img)
        plt.title(f"Original Image {idx+1}")
        plt.axis('off')
        
        # 2. First principal component (Red channel)
        plt.subplot(N, 5, idx * 5 + 2)
        plt.imshow(pca_norm[:, :, 0], cmap='Reds')
        plt.title("PC1 (Red)")
        plt.axis('off')
        
        # 3. Second principal component (Green channel)
        plt.subplot(N, 5, idx * 5 + 3)
        plt.imshow(pca_norm[:, :, 1], cmap='Greens')
        plt.title("PC2 (Green)")
        plt.axis('off')
        
        # 4. Third principal component (Blue channel)
        plt.subplot(N, 5, idx * 5 + 4)
        plt.imshow(pca_norm[:, :, 2], cmap='Blues')
        plt.title("PC3 (Blue)")
        plt.axis('off')
        
        # 5. RGB combination
        plt.subplot(N, 5, idx * 5 + 5)
        plt.imshow(pca_resized)
        plt.title("PC1+PC2+PC3 RGB")
        plt.axis('off')
        
        # Print variance explained for last image (they should be similar)
        if idx == N - 1:
            explained_variance = pca.explained_variance_ratio_
            print(f"Explained variance ratio: {explained_variance}")
            print(f"Cumulative variance: {explained_variance.sum():.4f}")
    
    plt.tight_layout()
    
    # Save the plot with backbone type in the filename
    save_file = save_path / f"dino_pca_analysis_{backbone_type}.png"
    plt.savefig(save_file, dpi=150, bbox_inches='tight')
    print(f"PCA visualization saved to: {save_file}")
    
    plt.show()

if __name__ == "__main__":
    # Image URLs to download
    image_urls = [
        "https://ml-img.oss-cn-beijing.aliyuncs.com/000030.jpg",
        "https://ml-img.oss-cn-beijing.aliyuncs.com/cat.82.jpg", 
        "https://ml-img.oss-cn-beijing.aliyuncs.com/dog.332.jpg"
    ]
    
    # Create temporary directory for downloaded images
    temp_dir = Path("./temp_images")
    temp_dir.mkdir(exist_ok=True)
    
    # Download images
    image_paths = []
    for i, url in enumerate(image_urls):
        filename = f"image_{i+1}.jpg"
        save_path = temp_dir / filename
        
        if download_image(url, save_path):
            image_paths.append(save_path)
        else:
            print(f"Skipping {url} due to download failure")
    
    if not image_paths:
        print("No images downloaded successfully. Exiting.")
        exit(1)
    
    # Load images
    original_imgs = []
    img_tensors = []
    
    for img_path in image_paths:
        try:
            img = Image.open(img_path).convert('RGB')
            original_imgs.append(img)
            
            # Preprocess for DINO (without normalization as DinoFeatureExtractor handles it)
            img_tensor = preprocess_image_for_dino(img, input_size=448)
            img_tensors.append(img_tensor)
            
            print(f"Loaded image: {img_path} - Size: {img.size}")
        except Exception as e:
            print(f"Failed to load {img_path}: {e}")
    
    if not img_tensors:
        print("No images loaded successfully. Exiting.")
        exit(1)
    
    # Stack into batch
    img_batch = torch.stack(img_tensors, dim=0)
    print(f"Input batch shape: {img_batch.shape}")
    
    # Initialize DINO feature extractor
    print("Initializing DINO feature extractor...")
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    try:
        # Initialize with safer defaults and error handling
        extractor = DinoFeatureExtractor(
            device=device,
            backbone="dinov2",  # or "dino"
            input_size=448,
            backbone_type="vit_base",
            patch_size=14
        )
        
        print(f"Feature extractor initialized:")
        print(f"  - Backbone: {extractor.backbone}")
        print(f"  - Backbone type: {extractor.backbone_type}")
        print(f"  - Input size: {extractor.input_size}")
        print(f"  - Patch size: {extractor.vit_patch_size}")
        print(f"  - Feature dimension: {extractor.feature_dim}")
        
        # Extract features
        print("Extracting features...")
        with torch.no_grad():
            features = extractor.extract_features(img_batch)
        
        print(f"Extracted features shape: {features.shape}")
        # Expected shape: [batch_size, num_patches, feature_dim]
        
        # Visualize PCA analysis
        print("Generating PCA visualization...")
        visualize_pca_features(
            original_imgs=original_imgs,
            features=features,
            patch_size=extractor.vit_patch_size,
            save_dir="~/ostrl_ws/src/debug/",
            backbone_type=extractor.backbone_type
        )
        
        print("Analysis completed successfully!")
        
    except Exception as e:
        print(f"Error during feature extraction: {e}")
        print("This might be due to missing model dependencies.")
        print("Please ensure DINO/DINOv2 models are properly installed.")
    
    finally:
        # Clean up temporary files
        import shutil
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print("Cleaned up temporary files.")